'use client';

import { useEffect, useState } from 'react';
import {
  type CensusData,
  processPlaceholders,
} from '@/lib/homepage/placeholder-processor';

interface HomepageAnnouncementProps {
  censusData: CensusData;
  locale?: string;
}

interface AnnouncementSettings {
  enabled: boolean;
  text: string;
  type: 'info' | 'warning' | 'success' | 'destructive';
}

export function HomepageAnnouncement({
  censusData,
  locale = 'en-AU',
}: HomepageAnnouncementProps) {
  const [settings, setSettings] = useState<AnnouncementSettings | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchSettings = async () => {
      try {
        const response = await fetch('/api/homepage-announcement');

        if (response.ok) {
          const data = await response.json();
          setSettings(data);
        }
      } catch (error) {
        // Silent failure - announcements are non-critical
      } finally {
        setIsLoading(false);
      }
    };

    fetchSettings();
  }, []);

  // Don't render anything while loading or if disabled
  if (isLoading || !settings?.enabled || !settings.text.trim()) {
    return null;
  }

  // Process placeholders
  const processedText = processPlaceholders(settings.text, {
    censusData,
    locale,
  });

  // Don't render if processed text is empty
  if (!processedText.trim()) {
    return null;
  }

  // Process markdown-style links [text](url)
  const processLinks = (text: string) => {
    return text.replace(
      /\[([^\]]+)\]\(([^)]+)\)/g,
      '<a href="$2" class="underline hover:no-underline font-medium" target="_blank" rel="noopener noreferrer">$1</a>'
    );
  };

  const processedTextWithLinks = processLinks(processedText);

  // Modern subtle styling for inline announcement
  const getModernStyle = (type: string) => {
    switch (type) {
      case 'warning':
        return {
          container: 'bg-gradient-to-r from-amber-50/80 to-amber-100/60 border-amber-200/50 dark:from-amber-900/20 dark:to-amber-800/20 dark:border-amber-700/30',
          text: 'text-amber-800 dark:text-amber-200',
          accent: 'bg-amber-400/20'
        };
      case 'success':
        return {
          container: 'bg-gradient-to-r from-green-50/80 to-green-100/60 border-green-200/50 dark:from-green-900/20 dark:to-green-800/20 dark:border-green-700/30',
          text: 'text-green-800 dark:text-green-200',
          accent: 'bg-green-400/20'
        };
      case 'destructive':
        return {
          container: 'bg-gradient-to-r from-red-50/80 to-red-100/60 border-red-200/50 dark:from-red-900/20 dark:to-red-800/20 dark:border-red-700/30',
          text: 'text-red-800 dark:text-red-200',
          accent: 'bg-red-400/20'
        };
      default: // info
        return {
          container: 'bg-gradient-to-r from-blue-50/80 to-blue-100/60 border-blue-200/50 dark:from-blue-900/20 dark:to-blue-800/20 dark:border-blue-700/30',
          text: 'text-blue-800 dark:text-blue-200',
          accent: 'bg-blue-400/20'
        };
    }
  };

  const styles = getModernStyle(settings.type);

  return (
    <div className="fade-in mx-auto my-2 w-full animate-in duration-500 ease-out">
      <div className={`relative overflow-hidden rounded-lg border backdrop-blur-sm ${styles.container}`}>
        {/* Subtle accent line */}
        <div className={`absolute top-0 left-0 right-0 h-0.5 ${styles.accent}`} />

        <div className="px-4 py-3">
          <p
            className={`${styles.text} font-medium text-sm leading-relaxed text-center`}
            dangerouslySetInnerHTML={{ __html: processedTextWithLinks }}
          />
        </div>
      </div>
    </div>
  );
}
