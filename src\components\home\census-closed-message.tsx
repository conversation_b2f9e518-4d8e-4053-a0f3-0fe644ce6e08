'use client';

import { <PERSON><PERSON><PERSON>, CalendarOff, HelpCircle, Info } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { HomepageAnnouncement } from '@/components/home/<USER>';
import { Button } from '@/components/ui/button';
import type { CensusData } from '@/lib/homepage/placeholder-processor';

interface CensusClosedMessageProps {
  censusData?: CensusData;
  locale?: string;
}

export function CensusClosedMessage({
  censusData,
  locale = 'en-AU',
}: CensusClosedMessageProps = {}) {
  const router = useRouter();

  // Translation hooks
  const t = useTranslations('census');
  const tNav = useTranslations('navigation');

  return (
    <div className="mx-auto w-full max-w-lg">
      {/* Modern card container with glass morphism */}
      <div className="relative overflow-hidden rounded-2xl bg-white/80 backdrop-blur-sm border border-white/20 shadow-xl shadow-black/5 dark:bg-gray-900/80 dark:border-gray-700/20 dark:shadow-black/20">
        {/* Subtle gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-destructive/5 via-transparent to-destructive/10 pointer-events-none" />

        <div className="relative flex flex-col items-center space-y-8 p-8">
          {/* Enhanced icon with gradient background */}
          <div className="relative mb-2">
            <div className="absolute inset-0 rounded-full bg-gradient-to-br from-destructive/20 to-destructive/30 blur-lg" />
            <div className="relative inline-flex items-center justify-center rounded-full bg-gradient-to-br from-destructive/10 to-destructive/20 p-6 border border-destructive/20">
              <CalendarOff className="h-12 w-12 text-destructive" />
            </div>
          </div>

          {/* Main content with improved spacing */}
          <div className="space-y-4 text-center">
            <h1 className="font-bold text-3xl tracking-tight bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent">
              {t('censusCurrentlyClosed')}
            </h1>

            {/* Inline announcement with better integration */}
            {censusData && (
              <div className="w-full">
                <HomepageAnnouncement censusData={censusData} locale={locale} />
              </div>
            )}
          </div>

          {/* Enhanced Bible Quote section */}
          <div className="w-full max-w-md">
            <div className="relative rounded-xl bg-gradient-to-br from-primary/5 to-primary/10 p-6 border border-primary/10 dark:from-primary/10 dark:to-primary/20 dark:border-primary/20">
              {/* Decorative corner accent */}
              <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-primary/20 to-transparent rounded-bl-2xl" />

              <div className="relative flex items-start gap-4">
                <div className="flex-shrink-0 p-2 rounded-lg bg-primary/10 dark:bg-primary/20">
                  <BookOpen className="h-5 w-5 text-primary" />
                </div>
                <div className="flex-1">
                  <p className="mb-3 text-base italic leading-relaxed text-foreground/90">
                    &quot;For everything there is a season, and a time for every matter under heaven.&quot;
                  </p>
                  <p className="text-right text-muted-foreground text-sm font-medium">
                    — Ecclesiastes 3:1
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Modern divider with enhanced styling */}
          <div className="flex w-full items-center gap-4 px-6">
            <div className="h-px flex-1 bg-gradient-to-r from-transparent via-border to-transparent" />
            <div className="p-2 rounded-full bg-muted/50 border border-border/30">
              <Info className="h-4 w-4 text-muted-foreground" />
            </div>
            <div className="h-px flex-1 bg-gradient-to-r from-transparent via-border to-transparent" />
          </div>

          {/* Enhanced contact options */}
          <div className="w-full space-y-6">
            <Button
              className="flex w-full h-12 items-center justify-center gap-3 bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02] active:scale-[0.98] text-base font-semibold"
              onClick={() => router.push('/help')}
            >
              <HelpCircle className="h-5 w-5" />
              <span className="font-medium">
                {tNav('help')} & {tNav('faq')}
              </span>
            </Button>

            <p className="text-center text-muted-foreground text-base leading-relaxed">
              {t('checkBackNextPeriod')}
            </p>

            <div className="flex justify-center pt-2">
              <Link
                className="inline-flex items-center gap-1 text-muted-foreground text-sm hover:text-primary transition-colors duration-200 underline underline-offset-4 decoration-dotted"
                href="/admin/login"
              >
                {tNav('adminLogin')}
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
