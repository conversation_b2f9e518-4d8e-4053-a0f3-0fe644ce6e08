'use client';

import { <PERSON><PERSON><PERSON>, Users } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';
import { HomepageAnnouncement } from '@/components/home/<USER>';
import { Button } from '@/components/ui/button';
import { useCensusAuth } from '@/hooks/useCensusAuth';
import type { CensusData } from '@/lib/homepage/placeholder-processor';

interface CensusWelcomeBackProps {
  userName: string;
  userCode: string;
  censusData?: CensusData;
  locale?: string;
}

export function CensusWelcomeBack({
  userName,
  userCode,
  censusData,
  locale = 'en-AU',
}: CensusWelcomeBackProps) {
  const t = useTranslations('census');
  const tNav = useTranslations('navigation');
  const tLegal = useTranslations('legal');
  const router = useRouter();

  // Use client-side session for real-time updates
  const { session, isAuthenticated } = useCensusAuth();
  const [displayName, setDisplayName] = useState(userName);
  const [displayCode, setDisplayCode] = useState(userCode);

  // Update display name and code when session changes (real-time updates)
  useEffect(() => {
    if (isAuthenticated && session?.user) {
      setDisplayName(session.user.name);
      setDisplayCode(session.user.code);
    }
  }, [session, isAuthenticated]);

  const handleContinue = () => {
    router.push(`/census/${displayCode}`);
  };

  return (
    <div className="mx-auto w-full max-w-lg">
      {/* Modern card container with glass morphism */}
      <div className="relative overflow-hidden rounded-2xl bg-white/80 backdrop-blur-sm border border-white/20 shadow-xl shadow-black/5 dark:bg-gray-900/80 dark:border-gray-700/20 dark:shadow-black/20">
        {/* Subtle gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-primary/10 pointer-events-none" />

        <div className="relative flex flex-col gap-8 p-8">
          {/* Welcome Header with improved spacing */}
          <div className="flex flex-col items-center space-y-4 text-center">
            <h1 className="font-bold text-3xl leading-tight bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent">
              {t('welcomeBack', { name: displayName })}
            </h1>

            {/* Inline announcement with better integration */}
            {censusData && (
              <div className="w-full">
                <HomepageAnnouncement censusData={censusData} locale={locale} />
              </div>
            )}

            {/* Enhanced Bible Quote section */}
            <div className="w-full max-w-md mt-6">
              <div className="relative rounded-xl bg-gradient-to-br from-primary/5 to-primary/10 p-6 border border-primary/10 dark:from-primary/10 dark:to-primary/20 dark:border-primary/20">
                {/* Decorative corner accent */}
                <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-primary/20 to-transparent rounded-bl-2xl" />

                <div className="relative flex items-start gap-4">
                  <div className="flex-shrink-0 p-2 rounded-lg bg-primary/10 dark:bg-primary/20">
                    <BookOpen className="h-5 w-5 text-primary" />
                  </div>
                  <div className="flex-1">
                    <p className="mb-3 text-base italic leading-relaxed text-foreground/90">
                      &quot;Let us run with perseverance the race marked out for us.&quot;
                    </p>
                    <p className="text-right text-muted-foreground text-sm font-medium">
                      — Hebrews 12:1
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <p className="mt-4 text-balance text-muted-foreground text-base leading-relaxed">
              {t('continueWhereYouLeftOff')}
            </p>
          </div>

          {/* Action Section with modern button */}
          <div className="space-y-6">
            {/* Enhanced Action Button */}
            <Button
              className="w-full h-12 bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02] active:scale-[0.98] text-base font-semibold"
              onClick={handleContinue}
            >
              <Users className="mr-3 h-5 w-5" />
              {t('continue')}
            </Button>
          </div>

          {/* Modern Terms and Conditions Section */}
          <div className="space-y-4 pt-4 border-t border-border/50">
            {/* Elegant divider with improved styling */}
            <div className="relative text-center">
              <span className="bg-white/80 dark:bg-gray-900/80 px-4 py-1 rounded-full text-muted-foreground text-xs font-medium border border-border/30">
                {tLegal('termsAndConditions')}
              </span>
            </div>

            {/* Legal Links with better spacing */}
            <div className="text-center text-muted-foreground text-xs leading-relaxed px-2">
              {tLegal('byClickingContinue')}{' '}
              <Link
                className="text-primary hover:text-primary/80 underline underline-offset-2 transition-colors duration-200"
                href="/terms"
              >
                {tLegal('termsOfService')}
              </Link>{' '}
              {tLegal('and')}{' '}
              <Link
                className="text-primary hover:text-primary/80 underline underline-offset-2 transition-colors duration-200"
                href="/privacy-policy"
              >
                {tLegal('privacyPolicy')}
              </Link>
              .
            </div>

            {/* Admin Login Link with subtle styling */}
            <div className="text-center pt-2">
              <Link
                className="inline-flex items-center gap-1 text-muted-foreground text-sm hover:text-primary transition-colors duration-200 underline underline-offset-4 decoration-dotted"
                href="/admin/login"
              >
                {tNav('adminLogin')}
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
