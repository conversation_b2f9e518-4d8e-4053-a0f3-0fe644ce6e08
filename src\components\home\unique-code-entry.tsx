'use client';

import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { HomepageAnnouncement } from '@/components/home/<USER>';
import { Button } from '@/components/ui/button';
import { CountdownInput } from '@/components/ui/countdown-input';
import { Label } from '@/components/ui/label';
import { useCensusAuth } from '@/hooks/useCensusAuth';
import { useMessage } from '@/hooks/useMessage';
import type { CensusData } from '@/lib/homepage/placeholder-processor';
import { zodResolver } from '@/lib/utils/zod-resolver-compat';
import {
  type ClientCensusCodeFormValues,
  createClientCensusCodeSchema,
} from '@/lib/validation/client/census-client';

interface UniqueCodeEntryProps {
  censusData?: CensusData;
  locale?: string;
}

export function UniqueCodeEntry({
  censusData,
  locale = 'en-AU',
}: UniqueCodeEntryProps = {}) {
  const searchParams = useSearchParams();
  const { showError } = useMessage();
  const { signInWithCode, isSubmitting, rateLimit } = useCensusAuth();

  // Translation hooks
  const t = useTranslations('census');
  const tAuth = useTranslations('auth');
  const tCommon = useTranslations('common');
  const tNav = useTranslations('navigation');
  const tValidation = useTranslations('validation');
  const tLegal = useTranslations('legal');

  // Create translated schema
  const translatedSchema = createClientCensusCodeSchema(tValidation);

  const form = useForm<ClientCensusCodeFormValues>({
    resolver: zodResolver(translatedSchema),
    defaultValues: {
      code: '',
    },
  });

  // Auto-populate the code from URL query parameter on component mount
  useEffect(() => {
    // Get the code from the URL query parameter if searchParams exists
    if (searchParams) {
      const codeFromUrl = searchParams.get('code');

      // If code exists in URL, set it in the form
      if (codeFromUrl) {
        form.setValue('code', codeFromUrl);
      }
    }
  }, [searchParams, form]);

  // Handle form submission
  const onSubmit = async (data: ClientCensusCodeFormValues) => {
    // Don't submit if locked
    if (rateLimit.isLocked) {
      return;
    }

    try {
      // Strategic server validation before authentication attempt (hybrid approach)
      let currentStatus;
      try {
        currentStatus = await rateLimit.checkStatus();
      } catch (statusError) {
        // If status check fails, don't proceed with authentication for security
        if (process.env.NODE_ENV === 'development') {
          console.warn(
            'Rate limit status check failed, blocking authentication attempt:',
            statusError
          );
        }
        showError('serverError', 'auth');
        return;
      }

      // Use the returned status to avoid async race conditions
      if (currentStatus.isLocked) {
        return;
      }

      // Ensure we're using the census auth system
      if (process.env.NODE_ENV === 'development') {
        console.log('Home page: Using census auth system for login');
      }

      // Use the census auth hook to sign in with the code
      await signInWithCode(data.code);
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Census authentication error:', error);
      }
      showError('authenticationError', 'auth');
    }
  };

  // Handle manual submit (for countdown input)
  const handleSubmit = () => {
    if (!(rateLimit.isLocked || isSubmitting)) {
      form.handleSubmit(onSubmit)();
    }
  };

  return (
    <div className="mx-auto w-full max-w-lg">
      {/* Modern card container with glass morphism */}
      <div className="relative overflow-hidden rounded-2xl bg-white/80 backdrop-blur-sm border border-white/20 shadow-xl shadow-black/5 dark:bg-gray-900/80 dark:border-gray-700/20 dark:shadow-black/20">
        {/* Subtle gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-primary/10 pointer-events-none" />

        <div className="relative flex flex-col gap-8 p-8">
          {/* Welcome Header with improved spacing */}
          <div className="flex flex-col items-center space-y-4 text-center">
            <h1 className="font-bold text-3xl leading-tight bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent">
              {t('welcome')}
            </h1>

            {/* Inline announcement with better integration */}
            {censusData && (
              <div className="w-full">
                <HomepageAnnouncement censusData={censusData} locale={locale} />
              </div>
            )}

            <p className="mt-2 text-balance text-muted-foreground text-base leading-relaxed">
              {t('enterDetailsBelow')}
            </p>
          </div>
          {/* Enhanced Form Section */}
          <div className="space-y-6">
            <form className="space-y-6" onSubmit={form.handleSubmit(onSubmit)}>
              {/* Modern form field container */}
              <div className="space-y-3">
                <Label
                  htmlFor="code"
                  className="text-base font-semibold text-foreground"
                >
                  {tAuth('censusCode')}
                </Label>
                <div className="relative">
                  <CountdownInput
                    disabled={isSubmitting}
                    error={!!form.formState.errors.code}
                    isLocked={rateLimit.isLocked}
                    onChange={(value) => form.setValue('code', value)}
                    onQrCodeScanned={(code) => {
                      form.setValue('code', code);
                      // Clear any existing errors when QR code is scanned
                      form.clearErrors('code');
                    }}
                    onSubmit={handleSubmit}
                    placeholder={tAuth('censusCode')}
                    remainingTime={rateLimit.formattedTime}
                    showQrScanner={true}
                    value={form.watch('code')}
                  />
                </div>
                {form.formState.errors.code && (
                  <p className="text-destructive text-sm font-medium">
                    {form.formState.errors.code.message}
                  </p>
                )}
              </div>

              {/* Enhanced Submit Button */}
              <Button
                className="w-full h-12 bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02] active:scale-[0.98] text-base font-semibold"
                disabled={rateLimit.isLocked || isSubmitting}
                type="submit"
              >
                {isSubmitting ? tCommon('validating') : tCommon('startCensus')}
              </Button>
            </form>
          {/* Modern Terms and Conditions Section */}
          <div className="space-y-4 pt-4 border-t border-border/50">
            {/* Elegant divider with improved styling */}
            <div className="relative text-center">
              <span className="bg-white/80 dark:bg-gray-900/80 px-4 py-1 rounded-full text-muted-foreground text-xs font-medium border border-border/30">
                {tLegal('termsAndConditions')}
              </span>
            </div>

            {/* Legal Links with better spacing */}
            <div className="text-center text-muted-foreground text-xs leading-relaxed px-2">
              {tLegal('byClickingContinue')}{' '}
              <Link
                className="text-primary hover:text-primary/80 underline underline-offset-2 transition-colors duration-200"
                href="/terms"
              >
                {tLegal('termsOfService')}
              </Link>{' '}
              {tLegal('and')}{' '}
              <Link
                className="text-primary hover:text-primary/80 underline underline-offset-2 transition-colors duration-200"
                href="/privacy-policy"
              >
                {tLegal('privacyPolicy')}
              </Link>
              .
            </div>

            {/* Admin Login Link with subtle styling */}
            <div className="text-center pt-2">
              <Link
                className="inline-flex items-center gap-1 text-muted-foreground text-sm hover:text-primary transition-colors duration-200 underline underline-offset-4 decoration-dotted"
                href="/admin/login"
              >
                {tNav('adminLogin')}
              </Link>
            </div>
          </div>
          </div>
        </div>
      </div>
    </div>
  );
}
